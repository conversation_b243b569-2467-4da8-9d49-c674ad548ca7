import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import iconLink from "../../../svg/icons/icon_link.svg";
import iconLinkExternal from "../../../svg/icons/icon_linkExternal.svg";
import iconGift from "../../../svg/icons/icon_gift.svg";
import iconTagSport from "../../../svg/icons/icon_tag_sport.svg";
import { router, exitWithSlide } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage, getAppData } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgLockereWanderung from "../../../img/healthgoals/hg_lockereWanderung.jpg";
import { refreshDragScroll } from "../../../helpers/dragScroll.js";
import { showDialog } from "../../../helpers/dialog-helper.js";
import { progressGaugeCard } from "../../../webcomponents/progressGaugeCard.js";
import { setupChallengeResetMenu } from "../../../helpers/reset-helper.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
    ? "/healthgoals-overview/hg-fitUmgebung-active"
    : "/healthgoals-overview/hg-fitUmgebung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-lockere-wanderung-menu"
    })}
  `;
};

/**
 * List Item Content
 */
const trainingsplan = [
  {
    text: "1. Training: 7 - 10 km locker wandern",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/lockere-wanderung")
  },
  {
    text: "2. Training: 7 - 10 km locker wandern",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/lockere-wanderung")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const createSegments = (isStarted) => [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext oder Trainings-Bereich je nach Status -->
        ${isStarted ? html`
          <!-- Wenn Challenge gestartet ist -->
          ${sectionTitle("Trainings")}
          <p class="challenge-description content-padding">
            Ich empfehle Dir Routen bei komoot, die genau passend für diese Challenge sind.
          </p>

          <div class="content-padding content-no-bottom-padding content-no-top-padding">
            ${buttonTextIcon("Passende komoot-Routen zeigen", iconLinkExternal, "right")}
          </div>
        ` : html`
          <!-- Wenn Challenge noch nicht gestartet ist -->
          <p class="challenge-description content-padding">
            In malerischer Natur ist nicht nur der Alltagsstress weit weg. Wandern trainiert auch Deinen Körper und kurbelt Deinen Kalorienverbrauch an.
          </p>

          <!-- Grüne Info-Card mit Bullet Points - nur anzeigen wenn Challenge nicht gestartet ist -->
          <div class="standard-container content-padding">
            ${infoCardBullets({
              title: "Was wirst Du erreichen?",
              bulletPoints: [
                "Du stärkst Dein Herz-Kreislauf-System.",
                "Du verbringst mehr Zeit in der Natur.",
                "Du unterstützt Dein Immunsystem.",
                "Du lernst Deine Umgebung besser kennen."
              ],
              background: "--info-card-background-green"
            })}
          </div>
        `}

        <!-- Komoot Premium Gutschein - nur anzeigen wenn Challenge aktiv -->
        ${isStarted ? html`
          <div class="standard-container content-padding">
            ${infoCardPlain({
              icon: iconGift,
              title: "Ich schenke Dir komoot Premium",
              text: "Jetzt Gutschein einlösen und \"komoot\" Premium für 90 Tage nutzen (exklusiv für AOK PLUS-Versicherte – endet automatisch).",
              linkText: "Gutschein einlösen",
              backgroundColor: "var(--info-card-background-yellow)"
            })}
          </div>
        ` : ''}

        ${isStarted ? '' : html`
          ${sectionTitle("Diese Übungen erwarten Dich")}
        `}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgLockereWanderung,
              title: "Lockere Wanderung",
              altText: "Lockere Wanderung",
              link: "/training/lockere-wanderung"
            })}
            ${imageCardNarrow({
              imgPath: imgLockereWanderung,
              title: "Lockere Wanderung",
              altText: "Lockere Wanderung",
              link: "/training/lockere-wanderung"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen, nur anzeigen wenn Challenge nicht gestartet ist -->
        ${isStarted ? '' : html`
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Wander-Übung machen:
            <ul>
              <li>Wenn Du eine Bein- oder Fußverletzung hast.</li>
              <li>Wenn Du bei der Übung Schmerzen in den Beinen verspürst.</li>
              <li>Wenn Du schwanger bist.</li>
            </ul>
            <p><strong>Hinweise:</strong> Halte während der Übung bitte nicht den Atem an. Wende Dich an einen Arzt, wenn Du bei der Durchführung dieser Übung einen der oben genannten Schmerzen verspürst.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        `}

      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

/**
 * Zeigt den Dialog zur App-Verbindung an
 */
const showConnectionDialog = () => {
  // Setze den Challenge-Kontext für die Tracker-Verbindung
  appStorage.setCurrentChallengeContext('lockereWanderung');

  showDialog({
    title: "Verbinde bitte Deine Fitness-App",
    text: unsafeHTML("<p>Wähle mindestens eine der folgenden Verbindungen aus, bevor Du mit dieser Challenge startest.</p> <p>Verbinde Dich entweder mit der komoot-App und lass' Dich darüber navigieren oder verbinde Dein Fitness-Armband und starte eine Aktivität.</p> <p>Für die Bonifizierung prüft die NAVIDA-App Deine Trainingsdaten sobald Du den Button \"Training erfassen\" drückst.</p>"),
    icon: iconLink,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Weiter",
      link: "/tracker-connect"
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Scrollt animiert zum oberen Bereich der Seite
 */
const scrollToTop = () => {
  setTimeout(() => {
    const pageContent = document.querySelector(".page-content");
    if (pageContent) {
      console.log("Scrolle nach oben...");
      pageContent.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      console.log("Kein .page-content Container gefunden");
    }
  }, 300);
};

/**
 * Aktualisiert die UI direkt für eine abgeschlossene Challenge ohne vollständiges Re-Rendering
 */
const updateUIForCompletedChallenge = () => {
  console.log("Aktualisiere UI für abgeschlossene Challenge...");

  // 1. Pill von "Gerade Aktiv" zu "Abgeschlossen" ändern
  const pillsContainer = document.querySelector('.pills-container');
  if (pillsContainer) {
    pillsContainer.innerHTML = `
      <div class="pill" style="background-color: var(--pill-green-background); color: var(--pill-green-text);">
        Abgeschlossen
      </div>
    `;
    console.log("Pill zu 'Abgeschlossen' geändert");
  }

  // 2. Training-Container CSS-Klasse ändern
  const trainingContainer = document.querySelector('.training-done-container');
  if (trainingContainer) {
    trainingContainer.classList.remove('training-done-container');
    trainingContainer.classList.add('training-done-container-inactive');
    console.log("Training-Container Klasse zu 'inactive' geändert");
  }

  // 3. Button ausblenden
  const button = trainingContainer?.querySelector('.button-standard');
  if (button) {
    button.style.display = 'none';
    console.log("Training erfassen Button ausgeblendet");
  }

  // 4. Paragraph-Text ausblenden
  const paragraph = trainingContainer?.querySelector('p');
  if (paragraph) {
    paragraph.style.display = 'none';
    console.log("Training erfassen Paragraph ausgeblendet");
  }
};

/**
 * Animiert die Fortschrittsanzeige von einem Wert zum anderen
 * @param {number} fromValue - Startwert
 * @param {number} toValue - Zielwert
 * @param {number} duration - Animationsdauer in Millisekunden
 */
const animateProgress = (fromValue, toValue, duration = 2000) => {
  const startTime = performance.now();
  const difference = toValue - fromValue;

  console.log(`Animation gestartet: ${fromValue}% -> ${toValue}%`);

  // Warte bis DOM-Elemente verfügbar sind
  const waitForElements = () => {
    const percentageElement = document.querySelector('.percentage-text');
    const progressArc = document.querySelector('path[stroke-dasharray]');
    
    if (!percentageElement || !progressArc) {
      console.log('DOM-Elemente noch nicht verfügbar, warte...');
      setTimeout(waitForElements, 50);
      return;
    }

    console.log('DOM-Elemente gefunden, starte Animation...');

    // Starte Animation wenn Elemente verfügbar sind
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing-Funktion für sanfte Animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.round(fromValue + (difference * easeOutQuart));

      // Aktualisiere die Anzeige
      percentageElement.textContent = `${currentValue} %`;

      // Aktualisiere den Arc - KORRIGIERTE Berechnung für centerY = 130, radius = 50
      const arcLength = (270 / 360) * 2 * Math.PI * 50; // 270° von Kreis mit radius 50
      const strokeDashoffset = arcLength * (1 - currentValue / 100);
      progressArc.style.strokeDashoffset = strokeDashoffset;

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        console.log(`Animation beendet bei ${currentValue}%`);
      }
    };

    requestAnimationFrame(animate);
  };

  waitForElements();
};

/**
 * Zeigt den Dialog zur Trainingserfassung an
 */
const showTrainingRecordDialog = () => {
  showDialog({
    title: "Wann hast Du trainiert?",
    text: unsafeHTML("Toll, dass Du Deine Tageschallenge erfolgreich durchgeführt hast.<br>Nach Deiner Bestätigung wird überprüft, ob Dein Fitness-Tracker etwas aufgezeichnet hat und Du ausreichend trainiert hast. Das kann bis zu zwei Tage dauern."),
    icon: iconTagSport,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Ich habe heute trainiert",
      onClick: () => {
        // Aktuelle Challenge-Daten abrufen
        const currentChallenge = appStorage._data.challenges?.lockereWanderung;
        if (!currentChallenge) {
          console.error("Challenge-Daten nicht gefunden");
          return;
        }

        const currentCompletedTrainings = currentChallenge.completedTrainings || 0;
        const totalTrainings = currentChallenge.totalTrainings || 2;

        // Prüfen, ob bereits alle Trainings absolviert wurden
        if (currentCompletedTrainings >= totalTrainings) {
          console.log("Alle Trainings bereits absolviert");
          return;
        }

        // Training erfassen
        const newCompletedTrainings = currentCompletedTrainings + 1;
        appStorage.updateChallenge('lockereWanderung', {
          completedTrainings: newCompletedTrainings
        });

        console.log(`Training erfasst: ${newCompletedTrainings} von ${totalTrainings}`);

        // DIREKTE Animation ohne Re-Rendering
        const oldPercentage = Math.round((currentCompletedTrainings / totalTrainings) * 100);
        const newPercentage = Math.round((newCompletedTrainings / totalTrainings) * 100);

        // Aktualisiere die Trainingsanzahl sofort im DOM
        const gaugeTexts = document.querySelectorAll('.gaugeText');
        gaugeTexts.forEach(gaugeText => {
          if (gaugeText && gaugeText.innerHTML.includes('von')) {
            gaugeText.innerHTML = `${newCompletedTrainings} von ${totalTrainings}<br>erledigt`;
          }
        });

        // Starte Animation direkt
        setTimeout(() => {
          scrollToTop();
          setTimeout(() => {
            console.log(`Direkte Animation: ${oldPercentage}% -> ${newPercentage}%`);
            animateProgress(oldPercentage, newPercentage);

            // Nach der Animation prüfen, ob Challenge abgeschlossen ist und UI direkt aktualisieren
            setTimeout(() => {
              if (newCompletedTrainings >= totalTrainings) {
                console.log("Challenge abgeschlossen, aktualisiere UI direkt...");

                // Direkte DOM-Manipulation statt Re-Rendering
                updateUIForCompletedChallenge();

                console.log("Challenge-Abschluss UI-Update abgeschlossen - bleibe auf aktueller Seite");
              }
            }, 2500); // Nach der Animation (2000ms) + etwas Puffer
          }, 800);
        }, 200);
      }
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Template für die "Lockere Wanderung" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateLockereWanderung = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Lockere Wanderung - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Lockere Wanderung - Healthgoal aktiv:', appStorage._data.activeHealthGoals.fitUmgebung);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  // Prüfen, ob die Challenge bereits gestartet wurde
  const appData = getAppData();
  const challengeStarted = appData.challenges &&
                          appData.challenges.lockereWanderung &&
                          appData.challenges.lockereWanderung.started;

  // Daten für die Gauge-Komponente
  const completedTrainings = challengeStarted ? appStorage._data.challenges.lockereWanderung.completedTrainings : 0;
  const totalTrainings = challengeStarted ? appStorage._data.challenges.lockereWanderung.totalTrainings : 2;

  // Prüfen, ob alle Trainings abgeschlossen sind
  const allTrainingsCompleted = challengeStarted && completedTrainings >= totalTrainings;

  // Debug-Ausgaben
  console.log('Challenge Status:', {
    challengeStarted,
    completedTrainings,
    totalTrainings,
    allTrainingsCompleted,
    challengeData: appStorage._data.challenges?.lockereWanderung
  });

  // Erstelle die Segmente mit dem aktuellen Status
  const segments = createSegments(challengeStarted);
  const segmentedControl = createSegmentedControl(segments);

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Lockere Wanderung</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${challengeStarted ?
          (allTrainingsCompleted ?
            pillsContainer([
              { text: "Abgeschlossen", color: "--pill-green-background", textColor: "--pill-green-text" }
            ]) :
            pillsContainer([
              { text: "Gerade Aktiv", color: "--pill-accent-blue-background", textColor: "--pill-accent-blue-text" }
            ])
          ) :
          pillsContainer([
            { text: "7 bis 10 km", color: "--pill-green-background", textColor: "--pill-green-text" },
            { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
            { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
          ])
        }
      </div>

      <!-- Progress Gauge Card - nur anzeigen, wenn die Challenge gestartet wurde -->
      ${challengeStarted ? html`
        <div class="content-padding">
          ${progressGaugeCard({
            title: "Das steht als nächstes an:",
            description: "1. Training: 7-10 km locker wandern",
            completedTrainings: completedTrainings,
            totalTrainings: totalTrainings
          })}
        </div>
      ` : ''}

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start/Erfassen Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('lockereWanderung');
        const canShowButton = appStorage._data.activeHealthGoals.fitUmgebung &&
                             appStorage._data.healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <div class="standard-container content-padding ${challengeStarted ? (allTrainingsCompleted ? 'training-done-container-inactive' : 'training-done-container') : ''}">
          ${challengeStarted ? html`
            <!-- Paragraph für Training erfassen -->
            <p class="content-top-padding caption center-text">
              ${allTrainingsCompleted
                ? "Du hast Dein Training für heute bereits erfasst. Morgen kannst Du die nächste Tageschallenge absolvieren und hier erfassen."
                : "Bestätige mir hier, dass Du trainiert hast, damit Deine Aktivität überprüft werden kann."
              }
            </p>
          ` : ''}

          ${!allTrainingsCompleted ? buttonStandard({
            text: challengeStarted ? "Training erfassen" : "Challenge jetzt starten",
            variant: "primary",
            onClick: challengeStarted
              ? showTrainingRecordDialog
              : showConnectionDialog
          }) : ''}
        </div>
      ` : html`
        <!-- Button ausgeblendet: Bedingungen nicht erfüllt -->
        <div class="standard-container content-padding">
          <p class="caption black-text">
            ${!appStorage._data.activeHealthGoals.fitUmgebung
              ? "Aktiviere zuerst das Gesundheitsziel, um Challenges zu starten."
              : !appStorage._data.healthGoalLevel
                ? "Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten."
                : appStorage.hasActiveChallenge() && !appStorage.isChallengeActive('lockereWanderung')
                  ? "Du hast bereits eine andere Challenge gestartet. Schließe diese zuerst ab oder setze sie zurück."
                  : "Challenge kann nicht gestartet werden."
            }
          </p>
        </div>
      `}

    </div>
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Challenge
 * Sollte nach dem Rendern der Seite aufgerufen werden
 */
export const initializeLockereWanderungResetMenu = () => {
  setTimeout(() => {
    setupChallengeResetMenu(
      "challenge-lockere-wanderung-menu",
      "lockereWanderung",
      "Lockere Wanderung",
      () => {
        // Callback nach Reset - zurück zur Health Goal Seite
        console.log("Challenge Lockere Wanderung wurde zurückgesetzt, navigiere zurück...");
        const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
          ? "/healthgoals-overview/hg-fitUmgebung-active"
          : "/healthgoals-overview/hg-fitUmgebung";
        router.navigate(backTarget);
      }
    );
  }, 100); // Kurze Verzögerung um sicherzustellen, dass das DOM gerendert ist
};

console.log("Challenge Lockere Wanderung loaded");
